<script lang="ts">
	import { Motion } from "svelte-motion";
	import { cn } from "$lib/utils/cn";
	import DockItem from "./DockItem.svelte";
	import type { Component } from "svelte";

	let {
		items,
		class: className = "",
		showBorderAnimation = true
	}: {
		items: {
			icon: Component;
			label: string;
			onclick?: () => void;
		}[];
		class?: string;
		showBorderAnimation?: boolean;
	} = $props();

	let isMobile = $state(false);

	// Detect mobile device and adjust animations accordingly
	$effect(() => {
		if (typeof window !== 'undefined') {
			isMobile = window.matchMedia('(pointer: coarse)').matches || 
			          window.matchMedia('(max-width: 768px)').matches;
		}
	});

	// Create derived animation object
	let floatingAnimation = $derived({
		initial: { y: 0 },
		animate: isMobile ? { y: 0 } : {
			y: [-2, 2, -2],
			transition: {
				duration: 4,
				repeat: Infinity,
				ease: "easeInOut"
			}
		}
	});
</script>

<div class={cn("w-full flex items-end justify-center p-2 pb-4", className)}>
	<Motion
		let:motion
		initial="initial"
		animate="animate"
		variants={floatingAnimation}
	>
		<div use:motion class="w-full max-w-4xl rounded-2xl flex items-center justify-center relative">
			<div
				class={cn(
					"relative flex items-center gap-1 p-2 rounded-2xl",
					"backdrop-blur-xl border shadow-lg",
					"bg-background/30 border-border",
					"hover:shadow-2xl transition-shadow duration-300",
					// Mobile optimizations
					isMobile && "gap-2 p-3 shadow-xl"
				)}
			>
				{#each items as item}
					<DockItem {...item} />
				{/each}
			</div>
		</div>
	</Motion>
</div>
