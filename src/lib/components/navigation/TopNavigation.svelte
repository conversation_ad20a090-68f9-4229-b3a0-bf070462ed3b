<script lang="ts">
	import { DropdownMenu } from 'bits-ui';
	
	export let user: any;
	
	function handleSignOut() {
		window.location.href = '/signout';
	}
	
	// Get user initials for avatar
	function getUserInitials(user: any): string {
		if (user?.name) {
			return user.name.split(' ').map((n: string) => n[0]).join('').substring(0, 2).toUpperCase();
		}
		if (user?.primaryEmail) {
			return user.primaryEmail.substring(0, 2).toUpperCase();
		}
		return 'U';
	}
</script>

<nav class="top-nav">
	<!-- Left: Logo -->
	<a href="/dashboard" class="logo-link">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="24" height="24" class="logo-icon">
			<rect width="40" height="40" rx="8" fill="#0066cc"/>
			<text x="20" y="28" text-anchor="middle" fill="white" font-family="system-ui" font-size="18" font-weight="600">SF</text>
		</svg>
		<span class="logo-text">SourceFlex</span>
	</a>

	<!-- Right side: Add New, Apps, and User menus -->
	<div class="nav-actions">
		<!-- Add New Menu -->
		<DropdownMenu.Root>
			<DropdownMenu.Trigger class="nav-button">
				<button class="nav-button-inner">
					<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<path d="M12 5v14m-7-7h14"/>
					</svg>
					<span>Create</span>
					<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<path d="m6 9 6 6 6-6"/>
					</svg>
				</button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content class="dropdown-content">
				<DropdownMenu.Item class="dropdown-item">New Job</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Add Candidate</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Create Report</DropdownMenu.Item>
			</DropdownMenu.Content>
		</DropdownMenu.Root>

		<!-- Apps Menu -->
		<DropdownMenu.Root>
			<DropdownMenu.Trigger class="nav-button">
				<button class="nav-button-inner">
					<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<rect width="7" height="7" x="3" y="3" rx="1"/>
						<rect width="7" height="7" x="14" y="3" rx="1"/>
						<rect width="7" height="7" x="14" y="14" rx="1"/>
						<rect width="7" height="7" x="3" y="14" rx="1"/>
					</svg>
					<span>Apps</span>
					<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
						<path d="m6 9 6 6 6-6"/>
					</svg>
				</button>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content class="dropdown-content">
				<DropdownMenu.Item class="dropdown-item">Dashboard</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Jobs</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Candidates</DropdownMenu.Item>
				<DropdownMenu.Separator class="dropdown-separator" />
				<DropdownMenu.Item class="dropdown-item">Interviews</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Reports</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Settings</DropdownMenu.Item>
			</DropdownMenu.Content>
		</DropdownMenu.Root>

		<!-- User Menu -->
		<DropdownMenu.Root>
			<DropdownMenu.Trigger class="user-button">
				<div class="user-avatar">
					{getUserInitials(user)}
				</div>
				<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
					<path d="m6 9 6 6 6-6"/>
				</svg>
			</DropdownMenu.Trigger>
			<DropdownMenu.Content class="dropdown-content">
				<DropdownMenu.Item class="dropdown-item">Profile</DropdownMenu.Item>
				<DropdownMenu.Item class="dropdown-item">Settings</DropdownMenu.Item>
				<DropdownMenu.Separator class="dropdown-separator" />
				<DropdownMenu.Item class="dropdown-item" onclick={handleSignOut}>Sign Out</DropdownMenu.Item>
			</DropdownMenu.Content>
		</DropdownMenu.Root>
	</div>
</nav>

<style>
	.top-nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 64px;
		padding: 0 1.5rem;
		background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
		color: white;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 50;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}
	
	.logo-link {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		text-decoration: none;
		color: white;
		font-weight: 600;
		font-size: 1.125rem;
		transition: opacity 0.2s ease;
	}
	
	.logo-link:hover {
		opacity: 0.9;
	}
	
	.logo-icon {
		flex-shrink: 0;
	}
	
	.logo-text {
		white-space: nowrap;
	}
	
	.nav-actions {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}
	
	.nav-button {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem 0.75rem;
		background: rgba(255, 255, 255, 0.1);
		border: none;
		border-radius: 6px;
		color: white;
		font-size: 0.875rem;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
	}
	
	.nav-button:hover {
		background: rgba(255, 255, 255, 0.2);
	}
	
	.user-avatar {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 2rem;
		height: 2rem;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 50%;
		font-size: 0.75rem;
		font-weight: 600;
	}
	
	:global(.dropdown-content) {
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		padding: 0.5rem;
		min-width: 160px;
		box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
		z-index: 50;
	}
	
	:global(.dropdown-item) {
		display: flex;
		align-items: center;
		padding: 0.5rem 0.75rem;
		color: #374151;
		font-size: 0.875rem;
		border-radius: 4px;
		cursor: pointer;
		transition: all 0.2s ease;
	}
	
	:global(.dropdown-item:hover) {
		background: #f3f4f6;
		color: #111827;
	}
	
	:global(.dropdown-separator) {
		height: 1px;
		background: #e5e7eb;
		margin: 0.25rem 0;
	}
</style>
