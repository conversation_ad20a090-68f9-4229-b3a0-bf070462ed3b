<script lang="ts">
	import type { Snippet } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import {
		LogOut,
		User,
		LayoutDashboard,
		Search,
		Music,
		Heart,
		Plus,
		Settings
	} from 'svelte-lucide';
	import { goto } from '$app/navigation';
	import { Dock } from '$lib/components/ui/dock';
	import * as Dialog from '$lib/components/ui/dialog';

	let { children }: { children: Snippet } = $props();
	let showSignOutDialog = $state(false);

	function handleSignOut() {
		window.location.href = '/signout';
	}

	function openSignOutDialog() {
		showSignOutDialog = true;
	}

	function navigateToDashboard() {
		goto('/dashboard');
	}

	const dockItems = [
		{ icon: Search as any, label: 'Search' },
		{ icon: LayoutDashboard as any, label: 'Dashboard', onclick: navigateToDashboard },
		{ icon: Music as any, label: 'Music' },
		{ icon: Heart as any, label: 'Favorites' },
		{ icon: Plus as any, label: 'Add New' },
		{ icon: Settings as any, label: 'Settings' },
		{ icon: User as any, label: 'Profile' },
		{ icon: LogOut as any, label: 'Sign Out', onclick: openSignOutDialog }
	];
</script>

<div class="min-h-screen bg-background">
	<main class="flex flex-1 flex-col gap-6 p-6 pt-4 bg-background/50 pb-20">
		<div class="mx-auto w-full max-w-7xl">
			{@render children?.()}
		</div>
	</main>
	
	<!-- Fixed dock at bottom -->
	<div class="fixed bottom-4 w-full flex justify-center">
		<Dock items={dockItems} />
	</div>

	<!-- Sign Out Confirmation Dialog -->
	<Dialog.Root bind:open={showSignOutDialog}>
		<Dialog.Content class="sm:max-w-md">
			<Dialog.Header class="text-center">
				<div class="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
					<LogOut class="w-6 h-6 text-red-600" />
				</div>
				<Dialog.Title class="text-lg font-semibold">Confirm Sign Out</Dialog.Title>
				<Dialog.Description class="text-sm text-muted-foreground mt-2">
					Are you sure you want to sign out? You will be redirected to the login page.
				</Dialog.Description>
			</Dialog.Header>
			<Dialog.Footer class="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 mt-6">
				<Button variant="outline" onclick={() => (showSignOutDialog = false)} class="w-full sm:w-auto">
					Cancel
				</Button>
				<Button onclick={handleSignOut} variant="destructive" class="w-full sm:w-auto">
					Sign Out
				</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</div>
